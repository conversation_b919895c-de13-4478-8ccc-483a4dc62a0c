import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import dbConnect from '@/lib/mongodb';
import Employee from '@/models/Employee';
import { sanitizeEmail, sanitizeName, validateApiInput } from '@/lib/security';
import { getDefaultRole } from '@/lib/roles';
import { getOrganizationFromRequest } from '@/lib/middleware/organization';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    validateApiInput(body, {
      name: { required: true, type: 'string', minLength: 2, maxLength: 100 },
      email: { required: true, type: 'string' },
      password: { required: true, type: 'string', minLength: 6, maxLength: 128 }
    });

    const { name, email, password } = body;

    // Sanitize inputs
    const sanitizedName = sanitizeName(name);
    const sanitizedEmail = sanitizeEmail(email);

    // Connect to database
    await dbConnect();

    // Get organization context
    const orgContext = await getOrganizationFromRequest(request);
    if (!orgContext) {
      return NextResponse.json(
        { error: 'Organization not found. Please check your domain configuration.' },
        { status: 404 }
      );
    }

    // Check if organization is active
    if (!orgContext.organization.isActive) {
      return NextResponse.json(
        { error: 'Organization is currently inactive' },
        { status: 403 }
      );
    }

    // Check subscription status
    if (orgContext.organization.subscription.status !== 'active') {
      return NextResponse.json(
        { error: 'Organization subscription is not active' },
        { status: 402 }
      );
    }

    // Check if user already exists within this organization
    const existingEmployee = await Employee.findOne({
      email: sanitizedEmail,
      organization: orgContext.organization._id
    });
    if (existingEmployee) {
      return NextResponse.json(
        { error: 'An account with this email already exists in this organization' },
        { status: 409 }
      );
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create new employee
    const employee = new Employee({
      organization: orgContext.organization._id,
      name: sanitizedName,
      email: sanitizedEmail,
      password: hashedPassword,
      role: getDefaultRole(),
    });

    await employee.save();

    // Return success (don't include password in response)
    return NextResponse.json(
      {
        message: 'Account created successfully',
        employee: {
          id: employee._id,
          name: employee.name,
          email: employee.email,
          role: employee.role,
          organization: {
            id: orgContext.organization._id,
            name: orgContext.organization.name,
            domain: orgContext.organization.domain,
            subdomain: orgContext.organization.subdomain
          }
        }
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Signup error:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    if (error.message.includes('Invalid') || error.message.includes('format')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
