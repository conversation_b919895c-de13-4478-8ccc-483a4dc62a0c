import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Organization from '@/models/Organization';
import { withAuth } from '@/lib/middleware/auth';

// GET /api/organizations/[id] - Get organization by ID (super admin only)
export const GET = withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    await dbConnect();
    const { id } = await params;

    const organization = await Organization.findById(id).populate('employeeCount');

    if (!organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(organization);

  } catch (error) {
    console.error('Error fetching organization:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireSuperAdmin: true });

// PUT /api/organizations/[id] - Update organization (super admin only)
export const PUT = withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    await dbConnect();
    const { id } = await params;
    const body = await request.json();
    
    const {
      name,
      domain,
      subdomain,
      description,
      industry,
      size,
      website,
      address,
      settings,
      subscription
    } = body;

    // Validate required fields
    if (!name || !domain || !subdomain) {
      return NextResponse.json(
        { error: 'Name, domain, and subdomain are required' },
        { status: 400 }
      );
    }

    // Check if domain or subdomain already exists (excluding current organization)
    const existingOrg = await Organization.findOne({
      $or: [
        { domain: domain.toLowerCase() },
        { subdomain: subdomain.toLowerCase() }
      ],
      _id: { $ne: id }
    });

    if (existingOrg) {
      return NextResponse.json(
        { error: 'Domain or subdomain already exists' },
        { status: 409 }
      );
    }

    // Update organization
    const updatedOrganization = await Organization.findByIdAndUpdate(
      id,
      {
        name: name.trim(),
        domain: domain.toLowerCase().trim(),
        subdomain: subdomain.toLowerCase().trim(),
        description: description?.trim(),
        industry: industry?.trim(),
        size: size || 'small',
        website: website?.trim(),
        address: address || {},
        ...(settings && {
          settings: {
            allowPublicProfiles: settings.allowPublicProfiles ?? true,
            requireEmailVerification: settings.requireEmailVerification ?? false,
            enableAchievements: settings.enableAchievements ?? true,
            enableThanks: settings.enableThanks ?? true,
            customBranding: settings.customBranding ?? false,
            maxEmployees: settings.maxEmployees || 50,
          }
        }),
        ...(subscription && {
          subscription: {
            plan: subscription.plan || 'free',
            status: subscription.status || 'active',
            startDate: subscription.startDate || new Date(),
            endDate: subscription.endDate,
            features: subscription.features || [],
          }
        })
      },
      { new: true, runValidators: true }
    );

    if (!updatedOrganization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedOrganization);

  } catch (error: any) {
    console.error('Error updating organization:', error);

    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Domain or subdomain already exists' },
        { status: 409 }
      );
    }

    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationErrors,
          message: validationErrors.join(', ')
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireSuperAdmin: true });

// DELETE /api/organizations/[id] - Delete organization (super admin only)
export const DELETE = withAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    await dbConnect();
    const { id } = await params;

    // Check if organization has employees
    const Employee = (await import('@/models/Employee')).default;
    const employeeCount = await Employee.countDocuments({ organization: id });

    if (employeeCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete organization with existing employees' },
        { status: 400 }
      );
    }

    const deletedOrganization = await Organization.findByIdAndDelete(id);

    if (!deletedOrganization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Organization deleted successfully' });

  } catch (error) {
    console.error('Error deleting organization:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { requireSuperAdmin: true });
